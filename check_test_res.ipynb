{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cc078623", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/usr/local/lib/python3.10/dist-packages/transformers/utils/hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.\n", "  warnings.warn(\n"]}], "source": ["import json\n", "from transformers import AutoProcessor\n", "import sys \n", "import os \n", "# current_file_path = os.path.dirname(os.path.abspath(__file__))\n", "# module_path = os.path.join(current_file_path, \"../\")\n", "# sys.path.append(module_path)\n", "# from models.qwen2_5_vl import Qwen2VLRetForConditionalGeneration\n", "import torch \n", "import argparse\n", "from dataset.datasets_mbeir import QueryDataset, CandidateDataset\n", "from collators.mbeir_eval import MbeirQueryDataCollator, MbeirCandidateDataCollator\n", "from torch.utils.data import DataLoader \n", "import torch.nn.functional as F \n", "from accelerate import Accelerator\n", "import accelerate\n", "import numpy as np\n", "DATASET_QUERY_NUM_UPPER_BOUND = 500000\n", "DATASET_CAN_NUM_UPPER_BOUND = 10000000\n", "\n", "NUM_QUERIES = 30\n", "NUM_CANDIDATES_FOR_POOL = 300 # Number of candidates to use in the evaluation pool for simulated retrieval\n", "MAX_RETRIEVED_PER_QUERY = 50 # For simulated retrieval and recall calculation up to K=50\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2ecde0aa", "metadata": {}, "outputs": [], "source": ["\n", "def unhash_qid(hashed_qid):\n", "    dataset_id = hashed_qid // DATASET_QUERY_NUM_UPPER_BOUND\n", "    data_within_id = hashed_qid % DATASET_QUERY_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def unhash_did(hashed_did):\n", "    dataset_id = hashed_did // DATASET_CAN_NUM_UPPER_BOUND\n", "    data_within_id = hashed_did % DATASET_CAN_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def load_qrel(filename):\n", "    qrel = {}\n", "    qid_to_taskid = {}\n", "    with open(filename, \"r\") as f:\n", "        for line in f:\n", "            query_id, _, doc_id, relevance_score, task_id = line.strip().split()\n", "            if int(relevance_score) > 0:  # Assuming only positive relevance scores indicate relevant documents\n", "                if query_id not in qrel:\n", "                    qrel[query_id] = []\n", "                qrel[query_id].append(doc_id)\n", "                if query_id not in qid_to_taskid:\n", "                    qid_to_taskid[query_id] = task_id\n", "    print(f\"Retriever: Loaded {len(qrel)} queries from {filename}\")\n", "    print(\n", "        f\"Retriever: Average number of relevant documents per query: {sum(len(v) for v in qrel.values()) / len(qrel):.2f}\"\n", "    )\n", "    return qrel, qid_to_taskid\n", "\n", "def compute_recall_at_k(relevant_docs, retrieved_indices, k):\n", "    if not relevant_docs:\n", "        return 0.0 # Return 0 if there are no relevant documents\n", "\n", "    # Get the set of indices for the top k retrieved documents\n", "    top_k_retrieved_indices_set = set(retrieved_indices[:k])\n", "\n", "    # Convert the relevant documents to a set\n", "    relevant_docs_set = set(relevant_docs)\n", "\n", "    # Check if there is an intersection between relevant docs and top k retrieved docs\n", "    # If there is, we return 1, indicating successful retrieval; otherwise, we return 0\n", "    if relevant_docs_set.intersection(top_k_retrieved_indices_set):\n", "        return 1.0\n", "    else:\n", "        return 0.0\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "996a17f4", "metadata": {}, "outputs": [], "source": ["\n", "class Args:\n", "    def __init__(self):\n", "        # Define the environment variables from the command\n", "        _MODEL_ID = \"./checkpoints/LamRA-Ret\"\n", "        _ORIGINAL_MODEL_ID = \"Qwen/Qwen2-VL-7B-Instruct\"\n", "        _IMAGE_PATH_PREFIX = \"/mnt/tidalfs-hssh01/dataset/mmeb/M-BEIR\"\n", "\n", "        # Arguments passed in the command line\n", "        self.query_data_path: str = f\"{_IMAGE_PATH_PREFIX}/query/test/mbeir_xhs_task7_test.jsonl\"\n", "        self.query_cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/local/mbeir_xhs_task7_cand_pool.jsonl\"\n", "        self.cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/local/mbeir_xhs_task7_cand_pool.jsonl\"\n", "        self.instructions_path: str = f\"{_IMAGE_PATH_PREFIX}/instructions/query_instructions.tsv\"\n", "        self.qrels_path: str = f\"{_IMAGE_PATH_PREFIX}/qrels/test/mbeir_xhs_task7_test_qrels.txt\"\n", "        self.original_model_id: str = _ORIGINAL_MODEL_ID\n", "        self.image_path_prefix: str = _IMAGE_PATH_PREFIX\n", "        self.model_id: str = _MODEL_ID\n", "\n", "        # Argument with a default value from the argparse definition (not overridden in the command)\n", "        self.model_max_length: int = 1024"]}, {"cell_type": "markdown", "id": "2d81c621", "metadata": {}, "source": ["# xhs 评估指标验证"]}, {"cell_type": "code", "execution_count": 6, "id": "7ff290b6", "metadata": {}, "outputs": [], "source": ["args = Args()\n", "from dataset.datasets_mbeir import QueryDataset, CandidateDataset\n", "from PIL import Image\n", "cand_dataset = CandidateDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n", "query_dataset = QueryDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.query_cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5cd7ab1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retriever: Loaded 999 queries from /mnt/tidalfs-hssh01/dataset/mmeb/M-BEIR/qrels/test/mbeir_xhs_task7_test_qrels.txt\n", "Retriever: Average number of relevant documents per query: 4.24\n"]}], "source": ["qrel, _ = load_qrel(args.qrels_path)\n", "cand_pool = {d['did']:d for d in cand_dataset.cand_pool}"]}, {"cell_type": "code", "execution_count": 55, "id": "6b235fb3", "metadata": {}, "outputs": [], "source": ["\n", "def show_some_query_info(query_idx):\n", "    query, _ = query_dataset[query_idx]\n", "    qimg = query[0]['content'][0]['image']\n", "    qbox = query[0]['content'][0]['box']\n", "    # cands = cand_names_json[query_idx] # len 50\n", "    poscand_did_list = qrel[f'10:{query_idx+1}']\n", "    # print(poscand_did)\n", "    # for i in (1,5,10,50):\n", "    #     for poscand_did in poscand_did_list:\n", "    #         if poscand_did in cands[:i]:\n", "    #             print(f\"right in recall@{i}\")\n", "    #             break\n", "    imglist = [qimg]\n", "    box_list = [qbox]\n", "    print(qbox)\n", "    imglist.extend([ cand_pool[did]['img_path'] for did in poscand_did_list])\n", "    box_list.extend([ cand_pool[did]['box'] for did in poscand_did_list])\n", "    \n", "    show_group_imgs(imglist,box_list)\n", "    return query, poscand_did_list\n", "\n", "\n", "def show_some_cand(did):\n", "    some_cand = cand_pool[did]\n", "    return Image.open(some_cand['img_path'])\n", "\n", "def show_group_imgs(image_paths, box_list=None, output_path=None):\n", "    def draw(image, box):\n", "        from PIL import Image, ImageDraw\n", "        if box is None: return image\n", "        width, height = image.size\n", "        x0 = width * box[0]\n", "        y0 = height * box[1]\n", "        x1 = width * box[2]\n", "        y1 = height * box[3]\n", "\n", "        image = image.crop((x0, y0, x1, y1))\n", "        return image\n", "\n", "        \n", "        draw = ImageDraw.Draw(image)\n", "        # (x0, y0) is top-left, (x1, y1) is bottom-right\n", "        line_thickness = max(2, int(min(width, height) * 0.01))\n", "        draw.rectangle(\n", "            [(x0, y0), (x1, y1)], \n", "            outline=\"red\", \n", "            width=line_thickness\n", "        )\n", "        return image\n", "    images = []\n", "    if box_list is None: box_list = [None] * len(image_paths)\n", "    for path, box in zip(image_paths, box_list):\n", "        images.append(draw(Image.open(path), box).resize((224,224)))\n", "    \n", "    # 获取第一张图片的模式和大小\n", "    mode = images[0].mode\n", "    width, height = images[0].size\n", "    \n", "    # 检查所有图片是否模式一致\n", "    for img in images:\n", "        if img.mode != mode:\n", "            raise ValueError(\"所有图片必须具有相同的模式\")\n", "    \n", "    # 计算拼接后总宽度和高度\n", "    total_width = sum(img.width for img in images)\n", "    max_height = max(img.height for img in images)\n", "    \n", "    # 创建空白画布\n", "    result = Image.new(mode, (total_width, max_height), (0, 0, 0, 0))\n", "    \n", "    # 拼接图片\n", "    x_offset = 0\n", "    for img in images:\n", "        result.paste(img, (x_offset, 0))\n", "        x_offset += img.width\n", "    \n", "    # 显示结果\n", "    result.show()\n", "\n", "def diff_topk_cands(ours, baseline, topk=5):\n", "    strong, weak = [] , []\n", "    for query_idx in range(len(query_dataset)):\n", "        # for t in qrel[f'10:{query_idx+1}']:\n", "        t = np.random.choice(qrel[f'10:{query_idx+1}'])\n", "        if t in ours[query_idx][:topk] and (t not in baseline[query_idx][:topk]):\n", "            strong.append(query_idx)\n", "        elif t not in ours[query_idx][:topk] and (t in baseline[query_idx][:topk]):\n", "            weak.append(query_idx)\n", "    print(f\"strong:{len(strong)}, weak:{len(weak)}\")\n", "    return strong, weak\n"]}, {"cell_type": "code", "execution_count": 30, "id": "2a5373a3", "metadata": {}, "outputs": [], "source": ["# 展示poscand\n", "# print(poscand_did_list)\n", "# show_some_cand(poscand_did_list[0])"]}, {"cell_type": "markdown", "id": "2ff82d14", "metadata": {}, "source": ["# 第一组实验，发现效果变差了  note-1k-1top-1pos-filter"]}, {"cell_type": "code", "execution_count": 16, "id": "aee02a3a", "metadata": {}, "outputs": [], "source": ["rootdir = \"LamRA_Ret_eval_results\"\n", "\n", "# with open(\"mbeir_xhs_task7_test_qwen2_5-vl-7b_LEMUIR_tune_bs120_cand_names.json\", \"r\") as f:\n", "#     ourscand_names_json = json.load(f)\n", "\n", "with open(\"/mnt/tidalfs-hssh01/usr/liangxun/ICLR26/lemuir/LamRA_Ret_eval_results/mbeir_xhs_task7_test_dam_pretrain+lamrallm_cand_names.json\", \"r\") as f:\n", "    ourscand_names_json = json.load(f)    "]}, {"cell_type": "code", "execution_count": 17, "id": "33c01aa4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["strong:0, weak:0\n"]}], "source": ["strong, weak = diff_topk_cands(ourscand_names_json, ourscand_names_json)"]}, {"cell_type": "code", "execution_count": null, "id": "96d6d3f1", "metadata": {}, "outputs": [], "source": ["# 展示top 5 的cands\n", "# show_some_cand(cands[4])\n", "# 如果是ours\n", "query_idx = 7 #weak[1]\n", "show_some_query_info(query_idx)\n", "print(\"our results\")\n", "\n", "ourcands = ourscand_names_json[query_idx] # len 50\n", "show_group_imgs([cand_pool[ourcands[i]]['img_path'] for i in range(10)],box_list=[cand_pool[ourcands[i]]['box'] for i in range(10)])\n", "# print(\"baseline results\")\n", "# baselinecands = baselinecand_names_json[query_idx]\n", "# show_group_imgs([cand_pool[baselinecands[i]]['img_path'] for i in range(10)])"]}, {"cell_type": "code", "execution_count": null, "id": "1c1d0652", "metadata": {}, "outputs": [], "source": ["# 展示top 5 的cands\n", "# show_some_cand(cands[4])\n", "# 如果是ours\n", "query_idx = 9 #weak[1]\n", "show_some_query_info(query_idx)\n", "print(\"our results\")\n", "\n", "ourcands = ourscand_names_json[query_idx] # len 50\n", "show_group_imgs([cand_pool[ourcands[i]]['img_path'] for i in range(10)],box_list=[cand_pool[ourcands[i]]['box'] for i in range(10)])\n", "# print(\"baseline results\")\n", "# baselinecands = baselinecand_names_json[query_idx]\n", "# show_group_imgs([cand_pool[baselinecands[i]]['img_path'] for i in range(10)])"]}, {"cell_type": "code", "execution_count": 100, "id": "b3aac203", "metadata": {}, "outputs": [], "source": ["query, _ = query_dataset[0]\n", "# qimg = query[0]['content'][0]['image']\n", "# qbox = query[0]['content'][0]['box']"]}, {"cell_type": "code", "execution_count": 101, "id": "b2b08ac6", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'user',\n", "  'content': [{'type': 'image',\n", "    'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/active_search_1040g0mg3181i0slljk1g49uktbgs606gp305c58.jpg',\n", "    'box': [0.53019, 0.39151, 0.99304, 0.80995]},\n", "   {'type': 'text', 'text': '\\nSummarize above image in one word: '}]},\n", " {'role': 'assistant', 'content': [{'type': 'text', 'text': '<emb>.'}]}]"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": null, "id": "9dd06a46", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "71b50c12", "metadata": {}, "source": ["# 第二组，原来recall@10有所提高的"]}, {"cell_type": "code", "execution_count": 33, "id": "798a3459", "metadata": {}, "outputs": [], "source": ["rootdir = \"Lam<PERSON>_Ret_eval_results/note-693-10top-10cand-nofilter/\"\n", "\n", "with open(rootdir+\"mbeir_xhs_task7_test_qwen2_5-vl-7b_LEMUIR_tune_bs120_cand_names.json\", \"r\") as f:\n", "    ourscand_names_json = json.load(f)\n", "\n", "with open(rootdir+\"mbeir_xhs_task7_test_qwen2_5-vl-7b_LEMUIR_tune_nodam_cand_names.json\", \"r\") as f:\n", "    baselinecand_names_json = json.load(f)    "]}, {"cell_type": "code", "execution_count": 74, "id": "8205e066", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["strong:67, weak:64\n"]}], "source": ["strong, weak = diff_topk_cands(ourscand_names_json, baselinecand_names_json, topk=10)"]}, {"cell_type": "code", "execution_count": null, "id": "9f6e2bf9", "metadata": {}, "outputs": [], "source": ["# 展示top 5 的cands\n", "# show_some_cand(cands[4])\n", "# 如果是ours\n", "query_idx = strong[23]\n", "query, pos_cand = show_some_query_info(query_idx)\n", "print(\"our results\")\n", "\n", "ourcands = ourscand_names_json[query_idx] # len 50\n", "show_group_imgs([cand_pool[ourcands[i]]['img_path'] for i in range(10)])\n", "print(\"baseline results\")\n", "baselinecands = baselinecand_names_json[query_idx]\n", "show_group_imgs([cand_pool[baselinecands[i]]['img_path'] for i in range(10)])"]}, {"cell_type": "markdown", "id": "a51c13e0", "metadata": {}, "source": ["# 第3组， 最初的评估集"]}, {"cell_type": "code", "execution_count": 98, "id": "5ba705a3", "metadata": {}, "outputs": [], "source": ["rootdir = \"LamRA_Ret_eval_results/original-bug/\"\n", "\n", "with open(rootdir+\"mbeir_xhs_task7_test_qwen2_5-vl-7b_LEMUIR_tune_bs120_cand_names.json\", \"r\") as f:\n", "    ourscand_names_json = json.load(f)\n", "\n", "with open(rootdir+\"mbeir_xhs_task7_test_qwen2_5-vl-7b_LEMUIR_tune_nodam_cand_names.json\", \"r\") as f:\n", "    baselinecand_names_json = json.load(f)    "]}, {"cell_type": "code", "execution_count": 99, "id": "ad6ac98c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["strong:37, weak:31\n"]}], "source": ["strong, weak = diff_topk_cands(ourscand_names_json, baselinecand_names_json, topk=5)"]}, {"cell_type": "code", "execution_count": null, "id": "65cbb30e", "metadata": {}, "outputs": [], "source": ["# 展示top 5 的cands\n", "# show_some_cand(cands[4])\n", "# 如果是ours\n", "query_idx = strong[14]\n", "query, pos_cand = show_some_query_info(query_idx)\n", "print(\"our results\")\n", "\n", "ourcands = ourscand_names_json[query_idx] # len 50\n", "show_group_imgs([cand_pool[ourcands[i]]['img_path'] for i in range(10)])\n", "print(\"baseline results\")\n", "baselinecands = baselinecand_names_json[query_idx]\n", "show_group_imgs([cand_pool[baselinecands[i]]['img_path'] for i in range(10)])"]}, {"cell_type": "code", "execution_count": null, "id": "f8214fe0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}